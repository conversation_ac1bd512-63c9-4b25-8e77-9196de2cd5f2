'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function Hero() {
  return (
    <section 
      id="hero" 
      className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-[#f8f4f0] via-[#f3eded] to-[#fcb1ab]/30"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(92,61,36,0.1),transparent_70%)]" />
      </div>

      {/* Main Container */}
      <div className="container mx-auto px-4 md:px-6 py-8 md:py-16 lg:py-20 pt-32 md:pt-40 lg:pt-16 xl:pt-12 relative z-10">
        {/* Split Screen Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center min-h-[80vh] lg:min-h-[70vh]">
          
          {/* Left Side - Image */}
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 1.2, delay: 0.2 }}
            className="relative order-1 lg:order-1"
          >
            <div className="relative">
              {/* Main Image Container */}
              <div className="relative aspect-[4/5] lg:aspect-[3/4] rounded-3xl overflow-hidden shadow-2xl border-8 border-white/80 backdrop-blur-sm">
                <Image
                  src="/assets/WhatsApp Image 2025-06-28 at 14.40.07.jpeg"
                  alt="Kelly en Celena - First Born Society oprichters"
                  fill
                  className="object-cover"
                  priority
                />
                
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
              </div>

              {/* Decorative Elements */}
              <motion.div
                initial={{ rotate: 0, scale: 0 }}
                animate={{ rotate: 360, scale: 1 }}
                transition={{ duration: 2, delay: 1 }}
                className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-[#fcb1ab] to-[#f3eded] rounded-full shadow-lg border-4 border-white/50"
              />
              <motion.div
                initial={{ rotate: 0, scale: 0 }}
                animate={{ rotate: -360, scale: 1 }}
                transition={{ duration: 2, delay: 1.5 }}
                className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-[#5c3d24]/10 to-[#fcb1ab]/20 rounded-full shadow-lg border-4 border-white/30"
              />
            </div>
          </motion.div>

          {/* Right Side - Content Card */}
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 1.2, delay: 0.4 }}
            className="relative order-2 lg:order-2 flex items-center justify-center"
          >
            {/* Floating Card */}
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
              whileHover={{ scale: 1.02 }}
              className="relative bg-white/90 backdrop-blur-lg rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20 max-w-xl w-full"
            >
              {/* Background Gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-transparent rounded-3xl" />
              
              {/* Content */}
              <div className="relative z-10">
                {/* Badge */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.8, delay: 1.2 }}
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#5c3d24] to-[#8b5a3c] rounded-full text-white text-sm font-medium mb-6"
                >
                  ✨ Kraamzorg met Hart & Ziel
                </motion.div>

                {/* Main Heading */}
                <motion.h1
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 1, delay: 1.4 }}
                  className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-[#5c3d24] leading-tight mb-6"
                >
                  First Born Society
                </motion.h1>

                {/* Subtitle */}
                <motion.p
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 1, delay: 1.6 }}
                  className="text-lg md:text-xl text-gray-700 font-body leading-relaxed mb-8"
                >
                  Waar Kelly en Celena families begeleiden naar <span className="text-[#fcb1ab] font-semibold">de beste start mogelijk</span>. 
                  Van zwangerschap tot kraamtijd - jullie dromen worden werkelijkheid.
                </motion.p>

                {/* Call to Action Buttons */}
                <motion.div
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 1, delay: 1.8 }}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-[#5c3d24] to-[#8b5a3c] text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
                  >
                    <span>Ontdek Onze Verhaal</span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-white/80 backdrop-blur-sm text-[#5c3d24] px-8 py-4 rounded-full font-semibold text-lg border-2 border-[#5c3d24]/20 hover:border-[#5c3d24]/40 transition-all duration-300 flex items-center justify-center gap-2"
                  >
                    <span>Neem Contact Op</span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </motion.button>
                </motion.div>

                {/* Trust Indicators */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 1, delay: 2 }}
                  className="flex items-center gap-6 mt-8 pt-8 border-t border-gray-200/50"
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#5c3d24]">2+</div>
                    <div className="text-sm text-gray-600">Jaar ervaring</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#5c3d24]">100%</div>
                    <div className="text-sm text-gray-600">Persoonlijke zorg</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#5c3d24]">❤️</div>
                    <div className="text-sm text-gray-600">Met hart & ziel</div>
                  </div>
                </motion.div>
              </div>

              {/* Decorative corner elements */}
              <div className="absolute top-4 right-4 w-3 h-3 bg-[#fcb1ab] rounded-full opacity-60" />
              <div className="absolute bottom-4 left-4 w-2 h-2 bg-[#5c3d24] rounded-full opacity-40" />
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Floating Action Button - Mobile */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 2.2 }}
        className="fixed bottom-6 right-6 lg:hidden z-50"
      >
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="bg-gradient-to-r from-[#5c3d24] to-[#8b5a3c] text-white p-4 rounded-full shadow-2xl"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </motion.button>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 2.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-[#5c3d24]/60"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="flex flex-col items-center gap-2"
        >
          <span className="text-sm font-medium">Scroll omlaag</span>
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </motion.div>
      </motion.div>
    </section>
  )
} 