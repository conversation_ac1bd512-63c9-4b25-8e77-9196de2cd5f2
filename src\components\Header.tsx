'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6 }}
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/20 backdrop-blur-lg border-b border-white/20 shadow-xl py-2 md:py-4 lg:py-6' 
          : 'bg-gradient-to-b from-black/10 via-white/15 to-transparent backdrop-blur-sm py-4 md:py-6 lg:py-10'
      }`}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-center">
          {/* Navigation Layout */}
          <div className="flex items-center gap-4 md:gap-16 lg:gap-24 xl:gap-32">
            {/* Left Navigation */}
            <div className="hidden md:flex items-center gap-12 lg:gap-16">
              <button
                onClick={() => scrollToSection('top')}
                className="text-[#5c3d24] hover:text-[#4a2e1a] transition-colors font-medium text-base lg:text-lg uppercase tracking-wider"
              >
                HOME
              </button>
              <button
                onClick={() => scrollToSection('introductie')}
                className="text-[#5c3d24] hover:text-[#4a2e1a] transition-colors font-medium text-base lg:text-lg uppercase tracking-wider"
              >
                INTRODUCTIE
              </button>
              <button
                onClick={() => scrollToSection('over-ons')}
                className="text-[#5c3d24] hover:text-[#4a2e1a] transition-colors font-medium text-base lg:text-lg uppercase tracking-wider"
              >
                OVER ONS
              </button>
            </div>

            {/* Center Logo - Mobile Responsive */}
            <div className="flex-shrink-0">
              <button
                onClick={() => scrollToSection('top')}
                className={`relative transition-all duration-300 ${
                  isScrolled 
                    ? 'w-12 h-12 sm:w-16 sm:h-16 md:w-24 md:h-24 lg:w-32 lg:h-32' 
                    : 'w-16 h-16 sm:w-20 sm:h-20 md:w-32 md:h-32 lg:w-40 lg:h-40 xl:w-48 xl:h-48'
                }`}
              >
                <Image
                  src="/logo.png"
                  alt="First Born Society Logo"
                  fill
                  className="object-contain hover:scale-105 transition-transform duration-300"
                  priority
                />
              </button>
            </div>

            {/* Right Navigation */}
            <div className="hidden md:flex items-center gap-12 lg:gap-16">
              <button
                onClick={() => scrollToSection('missie')}
                className="text-[#5c3d24] hover:text-[#4a2e1a] transition-colors font-medium text-base lg:text-lg uppercase tracking-wider"
              >
                MISSIE
              </button>
              <button
                onClick={() => scrollToSection('galerij')}
                className="text-[#5c3d24] hover:text-[#4a2e1a] transition-colors font-medium text-base lg:text-lg uppercase tracking-wider"
              >
                GALERIJ
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-[#5c3d24] hover:text-[#4a2e1a] transition-colors font-medium text-base lg:text-lg uppercase tracking-wider"
              >
                CONTACT
              </button>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden absolute right-4">
            <button className="text-[#5c3d24] p-2">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </motion.header>
  )
} 